import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchCategories } from '../store/slices/categoriesSlice'
import type { RootState, AppDispatch } from '../store'
import { List, Spin, Alert } from 'antd'

const Categories: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { items, loading, error } = useSelector((state: RootState) => state.categories)

  useEffect(() => {
    dispatch(fetchCategories())
  }, [dispatch])

  if (loading) return <Spin tip="Loading categories..." />
  if (error) return <Alert message="Error" description={error} type="error" showIcon />

  return (
    <List
      header={<div>Categories</div>}
      bordered
      dataSource={items}
      renderItem={item => (
        <List.Item>
          <List.Item.Meta
            title={item.name}
            description={item.description}
          />
        </List.Item>
      )}
    />
  )
}

export default Categories
