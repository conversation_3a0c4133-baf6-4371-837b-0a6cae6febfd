from fastapi import APIRouter
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI

from app.api.router import (
    r_categories,
    r_products,
    r_variants,
    r_customers,
    r_stores,
    r_orders,
    r_order_details,
)
from app.core.config import settings

app = FastAPI()

# CORS settings
origins = settings.all_cors_origins

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

api_router = APIRouter()
api_router.include_router(r_categories.router)
api_router.include_router(r_products.router)
api_router.include_router(r_variants.router)
api_router.include_router(r_customers.router)
api_router.include_router(r_stores.router)
api_router.include_router(r_orders.router)
api_router.include_router(r_order_details.router)

# Fix redirect trailing slash issue by setting strict_slashes=False
app.include_router(api_router, prefix=settings.API_V1_STR)

# Nếu bạn có các router đặc biệt cho môi trường local, có thể include thêm tại đây
# if settings.ENVIRONMENT == "local":
#     api_router.include_router(private.router)
