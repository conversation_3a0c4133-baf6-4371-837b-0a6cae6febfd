export interface Category {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  category_id: string;
  created_at: string;
  updated_at: string;
  variants: Variant[];
}

export interface Variant {
  id: string;
  product_id: string;
  sku: string;
  price: number;
  stock: number;
  created_at: string;
  updated_at: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  created_at: string;
  updated_at: string;
}

export interface Store {
  id: string;
  name: string;
  address: string;
  phone: string;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  customer_id: string;
  store_id: string;
  total_amount: number;
  status: string;
  created_at: string;
  updated_at: string;
  order_details: OrderDetail[];
}

export interface OrderDetail {
  id: string;
  order_id: string;
  variant_id: string;
  quantity: number;
  price: number;
  created_at: string;
  updated_at: string;
}
