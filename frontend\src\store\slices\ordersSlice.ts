import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../utils/api'
import { Order } from '../../interfaces'

interface OrdersState {
  items: Order[];
  loading: boolean;
  error: string | null;
}

const initialState: OrdersState = {
  items: [],
  loading: false,
  error: null
}

export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async () => {
    const response = await api.get('/orders')
    return response.data
  }
)

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async (order: Partial<Order>) => {
    const response = await api.post('/orders', order)
    return response.data
  }
)

export const updateOrder = createAsyncThunk(
  'orders/updateOrder',
  async ({ id, ...order }: Partial<Order> & { id: string }) => {
    const response = await api.put(`/orders/${id}`, order)
    return response.data
  }
)

export const deleteOrder = createAsyncThunk(
  'orders/deleteOrder',
  async (id: string) => {
    await api.delete(`/orders/${id}`)
    return id
  }
)

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.loading = false
        state.items = action.payload
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch orders'
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.items.push(action.payload)
      })
      .addCase(updateOrder.fulfilled, (state, action) => {
        const index = state.items.findIndex(item => item.id === action.payload.id)
        if (index !== -1) {
          state.items[index] = action.payload
        }
      })
      .addCase(deleteOrder.fulfilled, (state, action) => {
        state.items = state.items.filter(item => item.id !== action.payload)
      })
  }
})

export const { reducer } = ordersSlice
