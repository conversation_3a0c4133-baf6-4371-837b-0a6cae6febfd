import React, { useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { fetchProducts } from '../store/slices/productsSlice'
import type { RootState, AppDispatch } from '../store'
import { List, Spin, Alert } from 'antd'
import { Product } from '../interfaces'

const Products: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>()
  const { items, loading, error } = useSelector((state: RootState) => state.products) as {
    items: Product[]
    loading: boolean
    error: string | null
  }

  useEffect(() => {
    dispatch(fetchProducts())
  }, [dispatch])

  if (loading) return <Spin tip="Loading products..." />
  if (error) return <Alert message="Error" description={error} type="error" showIcon />

  return (
    <List
      header={<div>Products</div>}
      bordered
      dataSource={items}
      renderItem={(item: Product) => (
        <List.Item>
          <List.Item.Meta
            title={item.name}
            description={item.description}
          />
        </List.Item>
      )}
    />
  )
}

export default Products
