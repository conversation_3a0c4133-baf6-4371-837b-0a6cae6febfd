import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import api from '../../utils/api'
import { Store } from '../../interfaces'

interface StoresState {
  items: Store[];
  loading: boolean;
  error: string | null;
}

const initialState: StoresState = {
  items: [],
  loading: false,
  error: null
}

export const fetchStores = createAsyncThunk(
  'stores/fetchStores',
  async () => {
    const response = await api.get('/stores')
    return response.data
  }
)

export const createStore = createAsyncThunk(
  'stores/createStore',
  async (store: Partial<Store>) => {
    const response = await api.post('/stores', store)
    return response.data
  }
)

export const updateStore = createAsyncThunk(
  'stores/updateStore',
  async ({ id, ...store }: Partial<Store> & { id: string }) => {
    const response = await api.put(`/stores/${id}`, store)
    return response.data
  }
)

export const deleteStore = createAsyncThunk(
  'stores/deleteStore',
  async (id: string) => {
    await api.delete(`/stores/${id}`)
    return id
  }
)

const storesSlice = createSlice({
  name: 'stores',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchStores.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchStores.fulfilled, (state, action) => {
        state.loading = false
        state.items = action.payload
      })
      .addCase(fetchStores.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch stores'
      })
      .addCase(createStore.fulfilled, (state, action) => {
        state.items.push(action.payload)
      })
      .addCase(updateStore.fulfilled, (state, action) => {
        const index = state.items.findIndex(item => item.id === action.payload.id)
        if (index !== -1) {
          state.items[index] = action.payload
        }
      })
      .addCase(deleteStore.fulfilled, (state, action) => {
        state.items = state.items.filter(item => item.id !== action.payload)
      })
  }
})

export const { reducer } = storesSlice
